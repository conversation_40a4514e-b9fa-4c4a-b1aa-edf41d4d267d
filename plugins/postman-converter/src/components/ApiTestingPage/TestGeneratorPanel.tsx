import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Box,
  Paper,
  makeStyles,
  CircularProgress,
  Tooltip,
  IconButton,
  TextField,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { CodeSnippet } from '@backstage/core-components';
import { ApiRequest, ApiResponse } from '../../types';

// Icons
import PlayArrowIcon from '@material-ui/icons/PlayArrow';
import RefreshIcon from '@material-ui/icons/Refresh';
import SaveIcon from '@material-ui/icons/Save';
import DeleteIcon from '@material-ui/icons/Delete';

const useStyles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(2),
  },
  codeEditor: {
    width: '100%',
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
    '& .MuiOutlinedInput-root': {
      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
      fontSize: '14px',
      lineHeight: '1.5',
      '& textarea': {
        resize: 'vertical',
      },
    },
  },
  buttonContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    marginTop: theme.spacing(2),
  },
  leftButtons: {
    display: 'flex',
    gap: theme.spacing(1),
  },
  rightButtons: {
    display: 'flex',
    gap: theme.spacing(1),
  },
}));

interface TestGeneratorPanelProps {
  request: ApiRequest;
  response: ApiResponse | null;
  onRunTests: (testScript: string) => void;
  onSaveTests: (testScript: string) => void;
  onGenerateTests?: () => void;
  isGenerating?: boolean;
  isRunning?: boolean;
  error?: string | null;
}

export const TestGeneratorPanel: React.FC<TestGeneratorPanelProps> = ({
  request,
  response,
  onRunTests,
  onSaveTests,
  onGenerateTests,
  isGenerating = false,
  isRunning = false,
  error = null,
}) => {
  const classes = useStyles();
  const [testScript, setTestScript] = useState<string>('');
  const [isEdited, setIsEdited] = useState<boolean>(false);

  // Update test script when request changes
  useEffect(() => {
    if (request.id && request.testScript) {
      setTestScript(request.testScript);
      setIsEdited(false);
    } else {
      setTestScript('');
      setIsEdited(false);
    }
  }, [request.id, request.testScript]);

  const handleGenerateTests = () => {
    if (onGenerateTests) {
      // Use the provided onGenerateTests callback
      onGenerateTests();
    } else {
      // Fallback to local generation if no callback provided
      if (!response) return;

      // Generate basic tests based on the response
      let generatedTests = '';

      // Add test for status code
      generatedTests += `// Test for status code\npm.test("Status code is ${response.status}", function () {\n    pm.response.to.have.status(${response.status});\n});\n\n`;

      // Add test for response time
      generatedTests += `// Test for response time\npm.test("Response time is acceptable", function () {\n    pm.expect(pm.response.responseTime).to.be.below(1000);\n});\n\n`;

      // If response is JSON, add tests for JSON structure
      if (response.headers['content-type']?.includes('application/json')) {
        try {
          const jsonBody = JSON.parse(response.body);

          // Add test for JSON validity
          generatedTests += `// Test for valid JSON\npm.test("Response is valid JSON", function () {\n    pm.response.to.be.json;\n});\n\n`;

          // Add tests for specific properties if they exist
          const keys = Object.keys(jsonBody);
          if (keys.length > 0) {
            generatedTests += `// Test for specific properties\npm.test("Response has expected properties", function () {\n`;
            keys.slice(0, 3).forEach(key => {
              generatedTests += `    pm.expect(pm.response.json()).to.have.property("${key}");\n`;
            });
            generatedTests += `});\n`;
          }
        } catch (e) {
          // If JSON parsing fails, just add a comment
          generatedTests += `// Note: Response appears to be JSON but could not be parsed\n`;
        }
      }

      setTestScript(generatedTests);
      setIsEdited(true);
    }
  };

  const handleRunTests = () => {
    onRunTests(testScript);
  };

  const handleSaveTests = () => {
    onSaveTests(testScript);
    setIsEdited(false);
  };

  const handleClearTests = () => {
    setTestScript('');
    setIsEdited(true);
  };

  const handleTestScriptChange = (newScript: string) => {
    setTestScript(newScript);
    setIsEdited(true);
  };

  return (
    <Paper className={classes.root}>
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h6">Test Script</Typography>
        <Button
          variant="outlined"
          color="primary"
          onClick={handleGenerateTests}
          disabled={!response || isGenerating}
          startIcon={isGenerating ? <CircularProgress size={20} /> : <RefreshIcon />}
        >
          {isGenerating ? 'Generating...' : 'Generate Tests'}
        </Button>
      </Box>

      <Typography variant="body2" color="textSecondary" paragraph>
        Write or generate test scripts to validate your API responses
      </Typography>

      {error && (
        <Alert severity="error" style={{ marginBottom: '16px' }}>
          {error}
        </Alert>
      )}

      <Box className={classes.codeEditor}>
        <TextField
          multiline
          rows={12}
          variant="outlined"
          fullWidth
          value={testScript}
          onChange={(e) => handleTestScriptChange(e.target.value)}
          placeholder="// Write your test scripts here
// Example:
pm.test('Status code is 200', function () {
    pm.response.to.have.status(200);
});

pm.test('Response time is less than 1000ms', function () {
    pm.expect(pm.response.responseTime).to.be.below(1000);
});"
          InputProps={{
            style: {
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              fontSize: '14px',
              lineHeight: '1.5',
            },
          }}
        />
      </Box>

      <Box className={classes.buttonContainer}>
        <div className={classes.leftButtons}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleRunTests}
            disabled={!testScript || isRunning || !response}
            startIcon={isRunning ? <CircularProgress size={20} /> : <PlayArrowIcon />}
          >
            {isRunning ? 'Running...' : 'Run Tests'}
          </Button>
          <Button
            variant="outlined"
            color="default"
            onClick={handleClearTests}
            disabled={!testScript}
            startIcon={<DeleteIcon />}
          >
            Clear
          </Button>
        </div>
        <div className={classes.rightButtons}>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleSaveTests}
            disabled={!isEdited}
            startIcon={<SaveIcon />}
          >
            Save
          </Button>
        </div>
      </Box>
    </Paper>
  );
};
